{
  "compilerOptions": {
    "target": "ESNext",
    "lib": ["esnext", "dom"],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "noEmit": true,

    // 严格性和类型检查相关配置
    "strict": true,
    "strictNullChecks": true,
    "skipDefaultLibCheck": true,
    "skipLibCheck": true,

    // 模块和兼容性相关配置
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,

    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },

    // 类型声明相关配置
    // "types": ["node", "vite/client", "element-plus/global"]
    "types": ["node", "vite/client"]
  }, 
  "include": [
    "env.d.ts", "src/**/*", "src/**/*.vue","auto-imports.d.ts","components.d.ts"
  ],
  "exclude": [
    "node_modules", "dist"
  ]
}