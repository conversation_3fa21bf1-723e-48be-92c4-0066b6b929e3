import { Request, Response, NextFunction } from 'express';
import { getRedis, REDIS_KEYS } from '../config/redis';
import { AppError } from './errorHandler';
import { logger } from '../utils/logger';

interface RateLimitOptions {
  windowMs: number; // 时间窗口（毫秒）
  maxRequests: number; // 最大请求数
  message?: string; // 限制消息
  skipSuccessfulRequests?: boolean; // 跳过成功请求
  skipFailedRequests?: boolean; // 跳过失败请求
}

const defaultOptions: RateLimitOptions = {
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 100, // 100个请求
  message: '请求过于频繁，请稍后再试'
};

export function createRateLimit(options: Partial<RateLimitOptions> = {}) {
  const opts = { ...defaultOptions, ...options };

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const redis = getRedis();
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      const key = REDIS_KEYS.RATE_LIMIT(ip);

      // 获取当前请求计数
      const current = await redis.incr(key);

      // 如果是第一次请求，设置过期时间
      if (current === 1) {
        await redis.expire(key, Math.ceil(opts.windowMs / 1000));
      }

      // 检查是否超过限制
      if (current > opts.maxRequests) {
        const ttl = await redis.ttl(key);
        
        logger.warn(`IP ${ip} 触发限流，当前请求数: ${current}`);
        
        res.set({
          'X-RateLimit-Limit': opts.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(Date.now() + ttl * 1000).toISOString()
        });

        throw new AppError(opts.message!, 429);
      }

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': opts.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, opts.maxRequests - current).toString(),
        'X-RateLimit-Reset': new Date(Date.now() + opts.windowMs).toISOString()
      });

      next();
    } catch (error) {
      if (error instanceof AppError) {
        next(error);
      } else {
        logger.error('限流中间件错误:', error);
        next(); // 限流失败时不阻止请求
      }
    }
  };
}

// 默认限流中间件
export const rateLimiter = createRateLimit();

// API限流中间件
export const apiRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  maxRequests: 60, // 60个请求
  message: 'API请求过于频繁，请稍后再试'
});

// 严格限流中间件（用于敏感操作）
export const strictRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  maxRequests: 10, // 10个请求
  message: '操作过于频繁，请稍后再试'
});
