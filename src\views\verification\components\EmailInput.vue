<template>
  <div class="email-input-container">
    <el-form @submit.prevent="handleSearch">
      <el-form-item>
        <el-input
          ref="emailInputRef"
          v-model="localEmail"
          placeholder="请输入邮箱地址，如：<EMAIL>"
          size="large"
          :disabled="disabled"
          clearable
          @keyup.enter="handleSearch"
          @clear="handleClear"
        >
          <template #prefix>
            <el-icon><Message /></el-icon>
          </template>
          <template #suffix>
            <el-tooltip content="支持的邮箱域名：@xxyxhui.online" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
          <template #append>
            <el-button 
              type="primary" 
              :loading="loading"
              :disabled="!localEmail || disabled"
              @click="handleSearch"
            >
              <el-icon v-if="!loading"><Search /></el-icon>
              查询验证码
            </el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <!-- 快速选择邮箱 -->
    <div v-if="recentEmails.length > 0" class="recent-emails">
      <el-text type="info" size="small">最近使用：</el-text>
      <div class="recent-email-tags">
        <el-tag
          v-for="email in recentEmails"
          :key="email"
          size="small"
          type="info"
          effect="plain"
          class="recent-email-tag"
          @click="selectRecentEmail(email)"
        >
          {{ email }}
        </el-tag>
      </div>
    </div>

    <!-- 邮箱格式提示 -->
    <div class="email-hints">
      <el-text type="info" size="small">
        <el-icon><InfoFilled /></el-icon>
        支持的邮箱格式：<EMAIL>
      </el-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Message, 
  Search, 
  QuestionFilled, 
  InfoFilled 
} from '@element-plus/icons-vue'

interface Props {
  modelValue: string
  loading?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', email: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  disabled: false
})

const emit = defineEmits<Emits>()

// 本地状态
const localEmail = ref(props.modelValue)
const emailInputRef = ref()
const recentEmails = ref<string[]>([])

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  localEmail.value = newValue
})

// 监听本地值变化
watch(localEmail, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理搜索
const handleSearch = () => {
  const email = localEmail.value.trim()
  
  if (!email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    ElMessage.error('邮箱格式不正确')
    return
  }

  // 检查是否为支持的域名
  const supportedDomains = ['xxyxhui.online']
  const domain = email.split('@')[1]
  
  if (!supportedDomains.includes(domain)) {
    ElMessage.warning(`当前仅支持 ${supportedDomains.join(', ')} 域名的邮箱`)
  }

  // 保存到最近使用
  saveToRecentEmails(email)
  
  // 发出搜索事件
  emit('search', email)
}

// 处理清空
const handleClear = () => {
  localEmail.value = ''
  emit('update:modelValue', '')
}

// 选择最近使用的邮箱
const selectRecentEmail = (email: string) => {
  localEmail.value = email
  emit('update:modelValue', email)
  handleSearch()
}

// 保存到最近使用
const saveToRecentEmails = (email: string) => {
  try {
    let recent = JSON.parse(localStorage.getItem('recentEmails') || '[]')
    
    // 移除重复项
    recent = recent.filter((item: string) => item !== email)
    
    // 添加到开头
    recent.unshift(email)
    
    // 只保留最近5个
    recent = recent.slice(0, 5)
    
    localStorage.setItem('recentEmails', JSON.stringify(recent))
    recentEmails.value = recent
  } catch (error) {
    console.error('保存最近使用邮箱失败:', error)
  }
}

// 加载最近使用的邮箱
const loadRecentEmails = () => {
  try {
    const recent = JSON.parse(localStorage.getItem('recentEmails') || '[]')
    recentEmails.value = recent
  } catch (error) {
    console.error('加载最近使用邮箱失败:', error)
    recentEmails.value = []
  }
}

// 聚焦输入框
const focus = () => {
  emailInputRef.value?.focus()
}

// 暴露方法
defineExpose({
  focus
})

// 生命周期
onMounted(() => {
  loadRecentEmails()
})
</script>

<style scoped lang="scss">
.email-input-container {
  .recent-emails {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .recent-email-tags {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
    }

    .recent-email-tag {
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary-light-5);
        color: var(--el-color-primary);
      }
    }
  }

  .email-hints {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  :deep(.el-input-group__append) {
    .el-button {
      border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .email-input-container {
    .recent-emails {
      flex-direction: column;
      align-items: flex-start;
      gap: 6px;
    }

    :deep(.el-input-group__append) {
      .el-button {
        padding: 8px 12px;
        
        span {
          display: none;
        }
      }
    }
  }
}
</style>
