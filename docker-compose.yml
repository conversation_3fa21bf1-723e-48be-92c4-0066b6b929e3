version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: email-verification-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-rootpassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-email_verification}
      MYSQL_USER: ${MYSQL_USER:-appuser}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-apppassword}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - email-verification-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: email-verification-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redispassword}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - email-verification-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 3s
      retries: 5

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: email-verification-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: ${MYSQL_USER:-appuser}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-apppassword}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-email_verification}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispassword}
      IMAP_HOST: ${IMAP_HOST:-imap.xxyxhui.online}
      IMAP_PORT: ${IMAP_PORT:-993}
      IMAP_USER: ${IMAP_USER}
      IMAP_PASSWORD: ${IMAP_PASSWORD}
      IMAP_TLS: true
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your-encryption-key-32-chars}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "3000:3000"
    volumes:
      - backend_logs:/app/logs
    networks:
      - email-verification-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      timeout: 5s
      retries: 5
      start_period: 30s

  # 前端应用
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: email-verification-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:3000}
      VITE_WS_URL: ${VITE_WS_URL:-ws://localhost:3000}
    networks:
      - email-verification-network
    depends_on:
      backend:
        condition: service_healthy

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: email-verification-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - email-verification-network
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 5s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  email-verification-network:
    driver: bridge
