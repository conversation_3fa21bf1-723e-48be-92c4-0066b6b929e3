# 邮箱验证码管理系统

一个基于 Vue3 + Node.js 的邮箱验证码管理系统，支持实时接收和查询 Augment 平台的验证码。

## ✨ 功能特性

- 🔍 **实时验证码查询** - 输入邮箱地址即可查询最新验证码
- ⚡ **实时推送更新** - 使用 WebSocket 实现验证码实时推送
- ⏰ **倒计时显示** - 显示验证码剩余有效时间
- 📱 **响应式设计** - 完美支持移动端和桌面端
- 🔒 **安全可靠** - 支持多邮箱管理，数据加密存储
- 🐳 **Docker 部署** - 一键部署，开箱即用

## 🏗️ 技术架构

### 前端技术栈
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript
- **Element Plus** - Vue 3 组件库
- **Pinia** - Vue 状态管理
- **Vue Router** - 路由管理
- **Socket.IO Client** - WebSocket 客户端
- **Axios** - HTTP 客户端

### 后端技术栈
- **Node.js** - JavaScript 运行时
- **Express** - Web 应用框架
- **TypeScript** - 类型安全的 JavaScript
- **Socket.IO** - WebSocket 服务
- **MySQL** - 关系型数据库
- **Redis** - 内存数据库
- **node-imap** - IMAP 邮件客户端

### 部署技术
- **Docker** - 容器化部署
- **Docker Compose** - 多容器编排
- **Nginx** - 反向代理和静态文件服务

## 🚀 快速开始

### 环境要求

- Docker 20.0+
- Docker Compose 2.0+
- Node.js 18+ (开发环境)
- Yarn (开发环境)

### 生产环境部署

1. **克隆项目**
```bash
git clone <repository-url>
cd email-verification-system
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置您的邮箱信息
```

3. **启动系统**
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

4. **访问系统**
- 前端应用: http://localhost
- 后端API: http://localhost:3000

### 开发环境

1. **启动数据库服务**
```bash
docker-compose up -d mysql redis
```

2. **安装依赖**
```bash
# 前端依赖
yarn install

# 后端依赖
cd backend && npm install
```

3. **启动开发服务器**
```bash
# 启动后端
cd backend && npm run dev

# 启动前端
yarn dev
```

或者使用开发脚本：
```bash
chmod +x scripts/dev.sh
./scripts/dev.sh
```

## 📋 配置说明

### 邮箱配置

在 `.env` 文件中配置您的邮箱信息：

```env
# 邮件服务配置
IMAP_HOST=imap.xxyxhui.online
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-email-password
```

### 数据库配置

```env
# MySQL配置
MYSQL_ROOT_PASSWORD=rootpassword123
MYSQL_DATABASE=email_verification
MYSQL_USER=appuser
MYSQL_PASSWORD=apppassword123

# Redis配置
REDIS_PASSWORD=redispassword123
```

## 🎯 使用指南

### 1. 查询验证码

1. 访问系统首页
2. 点击"邮箱验证码查询"
3. 输入您的邮箱地址（如：<EMAIL>）
4. 点击"查询验证码"按钮
5. 系统将显示该邮箱的所有有效验证码

### 2. 实时更新

- 系统会自动监听新的验证码邮件
- 收到新验证码时会实时推送到前端
- 验证码会显示剩余有效时间倒计时

### 3. 复制验证码

- 点击验证码卡片可直接复制验证码
- 点击"复制"按钮复制验证码到剪贴板

## 🔧 API 接口

### 验证码相关

```http
# 获取验证码列表
GET /api/verification-codes/:email

# 获取最新验证码
GET /api/verification-codes/:email/latest

# 验证验证码
POST /api/verification-codes/:email/verify
```

### WebSocket 事件

```javascript
// 订阅邮箱验证码更新
socket.emit('subscribe', { email: '<EMAIL>' })

// 接收新验证码
socket.on('newVerificationCode', (data) => {
  console.log('新验证码:', data)
})
```

## 📁 项目结构

```
email-verification-system/
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── config/         # 配置文件
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由
│   │   ├── services/       # 服务层
│   │   └── utils/          # 工具函数
│   ├── Dockerfile
│   └── package.json
├── src/                    # 前端代码
│   ├── components/         # 公共组件
│   ├── stores/            # Pinia 状态管理
│   ├── views/             # 页面组件
│   └── router/            # 路由配置
├── nginx/                 # Nginx 配置
├── mysql/                 # MySQL 初始化脚本
├── scripts/               # 部署脚本
├── docker-compose.yml     # Docker 编排文件
└── README.md
```

## 🛠️ 开发指南

### 添加新功能

1. **后端 API**
   - 在 `backend/src/routes/` 添加路由
   - 在 `backend/src/services/` 添加业务逻辑

2. **前端页面**
   - 在 `src/views/` 添加页面组件
   - 在 `src/router/` 配置路由

3. **状态管理**
   - 在 `src/stores/` 添加 Pinia store

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码

## 🐛 故障排除

### 常见问题

1. **邮件服务连接失败**
   - 检查 IMAP 配置是否正确
   - 确认邮箱密码和服务器地址
   - 检查防火墙设置

2. **数据库连接失败**
   - 确认 MySQL 服务是否启动
   - 检查数据库配置信息
   - 查看数据库日志

3. **前端无法连接后端**
   - 检查后端服务是否启动
   - 确认端口配置是否正确
   - 检查 CORS 设置

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f mysql
docker-compose logs -f redis
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。
