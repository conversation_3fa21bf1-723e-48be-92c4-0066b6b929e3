@echo off
chcp 65001 >nul
echo 🚀 启动邮箱验证码管理系统...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 检查环境变量文件
if not exist .env (
    echo 📝 创建环境变量文件...
    copy .env.example .env >nul
    echo ⚠️  请编辑 .env 文件，配置您的邮箱信息
    echo    特别是以下配置项：
    echo    - IMAP_HOST: 您的IMAP服务器地址
    echo    - IMAP_USER: 您的邮箱地址
    echo    - IMAP_PASSWORD: 您的邮箱密码
    pause
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist mysql\data mkdir mysql\data
if not exist redis\data mkdir redis\data
if not exist backend\logs mkdir backend\logs
if not exist nginx\logs mkdir nginx\logs

REM 构建并启动服务
echo 🔨 构建并启动服务...
docker-compose up --build -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose ps

REM 显示访问信息
echo.
echo ✅ 系统启动完成！
echo.
echo 📱 访问地址：
echo    前端应用: http://localhost
echo    后端API: http://localhost:3000
echo    健康检查: http://localhost/health
echo.
echo 🔧 管理命令：
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo.
echo 📊 数据库连接信息：
echo    MySQL: localhost:3306
echo    Redis: localhost:6379
echo.

REM 检查服务健康状态
echo 🏥 检查服务健康状态...
timeout /t 5 /nobreak >nul

REM 检查后端健康状态
curl -f http://localhost:3000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务运行正常
) else (
    echo ❌ 后端服务可能未正常启动，请检查日志: docker-compose logs backend
)

REM 检查前端健康状态
curl -f http://localhost/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端服务运行正常
) else (
    echo ❌ 前端服务可能未正常启动，请检查日志: docker-compose logs nginx
)

echo.
echo 🎉 系统已成功启动！请在浏览器中访问 http://localhost
pause
