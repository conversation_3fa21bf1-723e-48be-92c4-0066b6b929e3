{"name": "vue3-demo2", "type": "module", "version": "0.0.0", "private": true, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72", "scripts": {"dev": "vite --open", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"element-plus": "^2.9.3", "normalize.css": "^8.0.1", "pinia": "^2.3.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "dayjs": "^1.11.10", "lodash-es": "^4.17.21"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@tsconfig/node22": "^22.0.0", "@types/node": "^22.10.2", "@types/lodash-es": "^4.17.12", "@unocss/eslint-plugin": "^65.4.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.18.0", "eslint-plugin-format": "^1.0.1", "npm-run-all2": "^7.0.2", "sass": "^1.83.4", "typescript": "~5.6.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.1.10"}, "engines": {"node": ">=18.0.0"}}