<template>
  <div class="verification-page">
    <!-- 页面标题 -->
    <el-page-header @back="$router.back()">
      <template #content>
        <div class="page-title">
          <el-icon class="title-icon"><Message /></el-icon>
          <span class="text-large font-600 mr-3">邮箱验证码查询</span>
          <el-tag v-if="isConnected" type="success" size="small">
            <el-icon><Connection /></el-icon>
            已连接
          </el-tag>
          <el-tag v-else type="danger" size="small">
            <el-icon><Close /></el-icon>
            未连接
          </el-tag>
        </div>
      </template>
    </el-page-header>

    <!-- 邮箱输入区域 -->
    <el-card class="email-input-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>输入邮箱地址</span>
          <el-text type="info" size="small">支持 @xxyxhui.online 域名邮箱</el-text>
        </div>
      </template>
      
      <EmailInput 
        v-model="inputEmail" 
        @search="handleEmailSearch"
        :loading="isLoading"
        :disabled="isLoading"
      />
    </el-card>

    <!-- 验证码统计 -->
    <div v-if="currentEmail" class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-statistic title="总验证码" :value="verificationCodes.length">
            <template #suffix>
              <el-icon><Document /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="有效验证码" :value="validCodes.length">
            <template #suffix>
              <el-icon style="color: #67c23a"><CircleCheck /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="已过期" :value="expiredCodes.length">
            <template #suffix>
              <el-icon style="color: #f56c6c"><CircleClose /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="当前邮箱" :value="currentEmail">
            <template #suffix>
              <el-icon><User /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 验证码列表 -->
    <div class="codes-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 错误状态 -->
      <el-alert
        v-else-if="error"
        :title="error"
        type="error"
        :closable="false"
        show-icon
        class="error-alert"
      />

      <!-- 空状态 -->
      <el-empty 
        v-else-if="!verificationCodes.length && !isLoading" 
        description="请输入邮箱地址查询验证码"
        :image-size="120"
      >
        <template #image>
          <el-icon size="120" color="#909399"><Message /></el-icon>
        </template>
        <el-button type="primary" @click="focusEmailInput">
          开始查询
        </el-button>
      </el-empty>

      <!-- 验证码列表 -->
      <div v-else class="codes-list">
        <div class="list-header">
          <h3>验证码列表</h3>
          <div class="list-actions">
            <el-button 
              size="small" 
              @click="refreshCodes"
              :loading="isLoading"
              :icon="Refresh"
            >
              刷新
            </el-button>
            <el-button 
              size="small" 
              type="danger"
              @click="clearAllCodes"
              :icon="Delete"
            >
              清空
            </el-button>
          </div>
        </div>

        <el-row :gutter="16">
          <el-col 
            v-for="code in verificationCodes" 
            :key="`${code.timestamp}-${code.code}`"
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
            class="code-col"
          >
            <VerificationCodeCard 
              :code="code"
              @copy="handleCopyCode"
              @verify="handleVerifyCode"
            />
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useVerificationStore } from '@/stores/verification'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Message, 
  Connection, 
  Close, 
  Document, 
  CircleCheck, 
  CircleClose, 
  User,
  Refresh,
  Delete
} from '@element-plus/icons-vue'

import EmailInput from './components/EmailInput.vue'
import VerificationCodeCard from './components/VerificationCodeCard.vue'

// 使用store
const verificationStore = useVerificationStore()
const {
  currentEmail,
  verificationCodes,
  isLoading,
  error,
  isConnected,
  validCodes,
  expiredCodes
} = storeToRefs(verificationStore)

// 本地状态
const inputEmail = ref('')

// 处理邮箱搜索
const handleEmailSearch = async (email: string) => {
  if (!email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    ElMessage.error('邮箱格式不正确')
    return
  }

  await verificationStore.fetchVerificationCodes(email)
}

// 处理复制验证码
const handleCopyCode = async (code: string) => {
  await verificationStore.copyToClipboard(code)
}

// 处理验证验证码
const handleVerifyCode = async (email: string, code: string) => {
  const result = await verificationStore.verifyCode(email, code)
  
  if (result.valid) {
    ElMessage.success('验证码有效')
  } else {
    ElMessage.warning(result.message || '验证码无效')
  }
}

// 刷新验证码
const refreshCodes = async () => {
  if (currentEmail.value) {
    await verificationStore.fetchVerificationCodes(currentEmail.value)
  }
}

// 清空所有验证码
const clearAllCodes = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有验证码吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    verificationStore.clearData()
    inputEmail.value = ''
    ElMessage.success('已清空所有验证码')
  } catch {
    // 用户取消
  }
}

// 聚焦邮箱输入框
const focusEmailInput = () => {
  // 这里可以通过ref聚焦输入框
  console.log('Focus email input')
}

// 生命周期
onMounted(() => {
  // 初始化WebSocket连接
  verificationStore.initWebSocket()
  
  // 启动定时器
  verificationStore.startTimer()
})

onUnmounted(() => {
  // 清理连接
  verificationStore.disconnect()
})
</script>

<style scoped lang="scss">
.verification-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;

    .title-icon {
      font-size: 24px;
      color: var(--el-color-primary);
    }
  }

  .email-input-card {
    margin: 20px 0;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .stats-section {
    margin: 20px 0;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }

  .codes-container {
    margin-top: 20px;

    .loading-container {
      padding: 20px;
    }

    .error-alert {
      margin-bottom: 20px;
    }

    .codes-list {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--el-border-color-light);

        h3 {
          margin: 0;
          color: var(--el-text-color-primary);
        }

        .list-actions {
          display: flex;
          gap: 8px;
        }
      }

      .code-col {
        margin-bottom: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .verification-page {
    padding: 10px;

    .stats-section {
      :deep(.el-statistic) {
        text-align: center;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
