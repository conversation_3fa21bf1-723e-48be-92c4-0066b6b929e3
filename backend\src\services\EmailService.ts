import Imap from 'node-imap';
import { simpleParser } from 'mailparser';
import { logger } from '../utils/logger';
import { VerificationCodeCache } from '../config/redis';
import { getDatabase } from '../config/database';
import { EventEmitter } from 'events';

export interface EmailConfig {
  id?: number;
  email_address: string;
  imap_server: string;
  imap_port: number;
  username: string;
  password: string;
  is_active: boolean;
}

export interface VerificationCode {
  email: string;
  code: string;
  sender: string;
  subject: string;
  timestamp: Date;
  expiresAt: Date;
}

export class EmailService extends EventEmitter {
  private imapConnections: Map<string, Imap> = new Map();
  private isListening = false;

  // 验证码提取正则表达式
  private readonly CODE_PATTERNS = [
    /verification code[:\s]*(\d{6})/i,
    /验证码[:\s]*(\d{6})/i,
    /code[:\s]*(\d{6})/i,
    /(\d{6})\s*is your verification code/i,
    /your code is[:\s]*(\d{6})/i,
    /(\d{6})\s*is your.*code/i,
    /code:\s*(\d{6})/i,
    /验证码：\s*(\d{6})/i
  ];

  // Augment相关发送方
  private readonly AUGMENT_SENDERS = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  async initialize() {
    try {
      const emailConfigs = await this.getEmailConfigs();
      
      for (const config of emailConfigs) {
        if (config.is_active) {
          await this.createImapConnection(config);
        }
      }
      
      logger.info(`初始化了 ${this.imapConnections.size} 个邮箱连接`);
    } catch (error) {
      logger.error('邮件服务初始化失败:', error);
      throw error;
    }
  }

  private async getEmailConfigs(): Promise<EmailConfig[]> {
    const db = getDatabase();
    const [rows] = await db.execute(
      'SELECT * FROM email_configs WHERE is_active = true'
    );
    return rows as EmailConfig[];
  }

  private async createImapConnection(config: EmailConfig) {
    try {
      const imap = new Imap({
        user: config.username,
        password: config.password,
        host: config.imap_server,
        port: config.imap_port,
        tls: true,
        tlsOptions: {
          rejectUnauthorized: false
        },
        authTimeout: 10000,
        connTimeout: 10000
      });

      // 连接事件处理
      imap.once('ready', () => {
        logger.info(`IMAP连接成功: ${config.email_address}`);
        this.openInbox(imap, config.email_address);
      });

      imap.once('error', (err) => {
        logger.error(`IMAP连接错误 ${config.email_address}:`, err);
        this.reconnectImap(config);
      });

      imap.once('end', () => {
        logger.warn(`IMAP连接结束: ${config.email_address}`);
        this.reconnectImap(config);
      });

      this.imapConnections.set(config.email_address, imap);
      imap.connect();

    } catch (error) {
      logger.error(`创建IMAP连接失败 ${config.email_address}:`, error);
    }
  }

  private openInbox(imap: Imap, email: string) {
    imap.openBox('INBOX', false, (err, box) => {
      if (err) {
        logger.error(`打开收件箱失败 ${email}:`, err);
        return;
      }

      logger.info(`收件箱已打开: ${email}`);
      
      // 监听新邮件
      imap.on('mail', (numNewMsgs) => {
        logger.info(`收到 ${numNewMsgs} 封新邮件: ${email}`);
        this.fetchNewEmails(imap, email);
      });

      // 获取最近的邮件
      this.fetchRecentEmails(imap, email);
    });
  }

  private fetchRecentEmails(imap: Imap, email: string) {
    // 获取最近1小时的邮件
    const since = new Date();
    since.setHours(since.getHours() - 1);

    imap.search(['UNSEEN', ['SINCE', since]], (err, results) => {
      if (err) {
        logger.error(`搜索邮件失败 ${email}:`, err);
        return;
      }

      if (results.length > 0) {
        this.processEmails(imap, email, results);
      }
    });
  }

  private fetchNewEmails(imap: Imap, email: string) {
    imap.search(['UNSEEN'], (err, results) => {
      if (err) {
        logger.error(`搜索新邮件失败 ${email}:`, err);
        return;
      }

      if (results.length > 0) {
        this.processEmails(imap, email, results);
      }
    });
  }

  private processEmails(imap: Imap, email: string, uids: number[]) {
    const fetch = imap.fetch(uids, { bodies: '', markSeen: true });

    fetch.on('message', (msg, seqno) => {
      let buffer = '';

      msg.on('body', (stream) => {
        stream.on('data', (chunk) => {
          buffer += chunk.toString('utf8');
        });

        stream.once('end', async () => {
          try {
            const parsed = await simpleParser(buffer);
            await this.extractVerificationCode(email, parsed);
          } catch (error) {
            logger.error(`解析邮件失败 ${email}:`, error);
          }
        });
      });
    });

    fetch.once('error', (err) => {
      logger.error(`获取邮件失败 ${email}:`, err);
    });
  }

  private async extractVerificationCode(email: string, parsed: any) {
    try {
      const sender = parsed.from?.value?.[0]?.address || parsed.from?.text || '';
      const subject = parsed.subject || '';
      const textContent = parsed.text || '';
      const htmlContent = parsed.html || '';

      // 检查是否来自Augment
      const isFromAugment = this.AUGMENT_SENDERS.some(augmentSender => 
        sender.toLowerCase().includes(augmentSender.toLowerCase())
      ) || subject.toLowerCase().includes('augment');

      if (!isFromAugment) {
        return; // 不是Augment的邮件，跳过
      }

      // 提取验证码
      const content = textContent + ' ' + htmlContent;
      let verificationCode = null;

      for (const pattern of this.CODE_PATTERNS) {
        const match = content.match(pattern);
        if (match && match[1]) {
          verificationCode = match[1];
          break;
        }
      }

      if (verificationCode) {
        // 设置过期时间（2分钟）
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + 2);

        // 保存到Redis
        await VerificationCodeCache.saveCode(email, verificationCode, sender, expiresAt);

        // 保存到数据库
        await this.saveToDatabase(email, verificationCode, sender, subject, expiresAt);

        // 发出事件
        this.emit('verificationCodeReceived', {
          email,
          code: verificationCode,
          sender,
          subject,
          timestamp: new Date(),
          expiresAt
        });

        logger.info(`提取到验证码: ${email} - ${verificationCode} (来自: ${sender})`);
      }

    } catch (error) {
      logger.error('提取验证码失败:', error);
    }
  }

  private async saveToDatabase(email: string, code: string, sender: string, subject: string, expiresAt: Date) {
    try {
      const db = getDatabase();
      await db.execute(
        `INSERT INTO verification_logs 
         (email_address, verification_code, sender, subject, received_at, expires_at) 
         VALUES (?, ?, ?, ?, NOW(), ?)`,
        [email, code, sender, subject, expiresAt]
      );
    } catch (error) {
      logger.error('保存验证码到数据库失败:', error);
    }
  }

  private async reconnectImap(config: EmailConfig) {
    setTimeout(() => {
      logger.info(`尝试重连IMAP: ${config.email_address}`);
      this.createImapConnection(config);
    }, 30000); // 30秒后重连
  }

  startListening() {
    if (this.isListening) return;
    
    this.isListening = true;
    logger.info('邮件监听服务已启动');
  }

  stopListening() {
    this.isListening = false;
    
    for (const [email, imap] of this.imapConnections) {
      imap.end();
      logger.info(`关闭IMAP连接: ${email}`);
    }
    
    this.imapConnections.clear();
    logger.info('邮件监听服务已停止');
  }

  async getVerificationCodes(email: string) {
    return await VerificationCodeCache.getCodes(email);
  }

  async getLatestVerificationCode(email: string) {
    return await VerificationCodeCache.getLatestCode(email);
  }
}
