import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

import { logger } from './utils/logger';
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';
import { errorHandler } from './middleware/errorHandler';
import { rateLimiter } from './middleware/rateLimiter';

// 路由
import verificationRoutes from './routes/verification';
import emailConfigRoutes from './routes/emailConfig';

// 服务
import { EmailService } from './services/EmailService';
import { WebSocketService } from './services/WebSocketService';

// 加载环境变量
dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;

// 中间件
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:5173",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(rateLimiter);

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API路由
app.use('/api/verification-codes', verificationRoutes);
app.use('/api/email-configs', emailConfigRoutes);

// 错误处理
app.use(errorHandler);

// 初始化服务
async function initializeServices() {
  try {
    // 连接数据库
    await connectDatabase();
    await connectRedis();
    
    // 初始化邮件服务
    const emailService = new EmailService();
    await emailService.initialize();
    
    // 初始化WebSocket服务
    const wsService = new WebSocketService(io);
    wsService.initialize();
    
    // 启动邮件监听
    emailService.startListening();
    
    logger.info('所有服务初始化完成');
  } catch (error) {
    logger.error('服务初始化失败:', error);
    process.exit(1);
  }
}

// 启动服务器
server.listen(PORT, async () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  await initializeServices();
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

export default app;
