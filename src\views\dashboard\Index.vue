<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToVerificationCodes = () => {
  router.push('/verification-codes')
}
</script>

<template>
  <div class="dashboard-container">
    <el-page-header>
      <template #content>
        <span class="text-large font-600">系统仪表板</span>
      </template>
    </el-page-header>

    <div class="dashboard-content">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>快速导航</span>
              </div>
            </template>

            <div class="navigation-grid">
              <el-card
                class="nav-card"
                shadow="hover"
                @click="navigateToVerificationCodes"
              >
                <div class="nav-content">
                  <el-icon size="48" color="#409EFF">
                    <Message />
                  </el-icon>
                  <h3>邮箱验证码查询</h3>
                  <p>查询和管理邮箱验证码，支持实时更新</p>
                </div>
              </el-card>

              <el-card class="nav-card" shadow="hover">
                <div class="nav-content">
                  <el-icon size="48" color="#67C23A">
                    <Setting />
                  </el-icon>
                  <h3>邮箱配置管理</h3>
                  <p>配置和管理邮箱IMAP设置</p>
                </div>
              </el-card>

              <el-card class="nav-card" shadow="hover">
                <div class="nav-content">
                  <el-icon size="48" color="#E6A23C">
                    <DataAnalysis />
                  </el-icon>
                  <h3>统计分析</h3>
                  <p>查看验证码接收统计和分析报告</p>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>系统状态</span>
              </div>
            </template>

            <el-descriptions :column="1" border>
              <el-descriptions-item label="系统版本">v1.0.0</el-descriptions-item>
              <el-descriptions-item label="运行状态">
                <el-tag type="success">正常运行</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="连接状态">
                <el-tag type="success">已连接</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>使用说明</span>
              </div>
            </template>

            <el-steps direction="vertical" :active="3">
              <el-step title="配置邮箱" description="添加您的邮箱IMAP配置" />
              <el-step title="查询验证码" description="输入邮箱地址查询验证码" />
              <el-step title="实时更新" description="自动接收新的验证码" />
            </el-steps>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-content {
    margin-top: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .navigation-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;

      .nav-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 12px;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-content {
          text-align: center;
          padding: 20px;

          h3 {
            margin: 16px 0 8px 0;
            color: var(--el-text-color-primary);
          }

          p {
            margin: 0;
            color: var(--el-text-color-regular);
            font-size: 14px;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;

    .navigation-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
