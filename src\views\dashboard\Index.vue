<script setup lang="ts">
// import { ref } from 'vue'

// import { reactive } from 'vue';

// const a = reactive(1);
const b = ref(123)
console.log(b.value)
const c = reactive({ d: 456 })
console.log(c.d)
</script>

<template>
  111
  <el-button>Default</el-button>
  <el-button type="primary">
    Primary
  </el-button>
  <el-button type="success">
    Success
  </el-button>
  <el-button type="info">
    Info
  </el-button>
  <el-button type="warning">
    Warning
  </el-button>
  <el-button type="danger">
    Danger
  </el-button>
</template>

<style scoped lang="scss">

</style>
