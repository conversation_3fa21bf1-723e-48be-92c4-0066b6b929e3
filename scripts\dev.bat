@echo off
chcp 65001 >nul
echo 🚀 启动开发环境...

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

REM 检查yarn是否安装
yarn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Yarn未安装，请先安装Yarn
    pause
    exit /b 1
)

REM 启动数据库服务（仅MySQL和Redis）
echo 🗄️  启动数据库服务...
docker-compose up -d mysql redis

REM 等待数据库启动
echo ⏳ 等待数据库启动...
timeout /t 10 /nobreak >nul

REM 安装前端依赖
echo 📦 安装前端依赖...
yarn install

REM 安装后端依赖
echo 📦 安装后端依赖...
cd backend
call npm install
cd ..

REM 创建后端环境变量文件
if not exist backend\.env (
    echo 📝 创建后端环境变量文件...
    copy backend\.env.example backend\.env >nul
)

echo.
echo ✅ 开发环境准备完成！
echo.
echo 📱 请在两个终端中分别运行：
echo    终端1 - 后端: cd backend ^&^& npm run dev
echo    终端2 - 前端: yarn dev
echo.
echo 📱 访问地址：
echo    前端应用: http://localhost:5173
echo    后端API: http://localhost:3000
echo.
echo 🔧 开发工具：
echo    前端热重载: 已启用
echo    后端热重载: 已启用
echo.
pause
