import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite' // 自动导入常用的 API 和函数
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite' // 自动解析和注册(导入) Vue 组件

import { defineConfig } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    // 自动导入配置 https://github.com/sxzz/element-plus-best-practices/blob/main/vite.config.ts
    AutoImport({
      // 导入 Vue 函数，如：ref, reactive, toRef 等
      imports: ['vue', '@vueuse/core', 'pinia', 'vue-router'],
      // 导入 Element Plus函数，如：ElMessage, ElMessageBox 等
      resolvers: [ElementPlusResolver()],
      eslintrc: {
        enabled: false, // 生成文件之后可以改为false了
        filepath: './.eslintrc-auto-import.json', // 将已配置的 API 作为全局变量，导出到配置文件
        globalsPropValue: true,
      },
      // 导入函数类型声明文件路径 (false: 生成文件之后可以改为false了)
      dts: false,
      // dts: "auto-imports.d.ts",
    }),
    Components({
      // 导入 Element Plus 组件
      resolvers: [ElementPlusResolver()],
      // 指定自定义组件位置(默认:src/components),自动处理 Vue 组件的导入和注册
      dirs: ['src/components', 'src/**/components'],
      // 导入组件类型声明文件路径 (false:关闭自动生成)
      dts: false,
      // dts: "components.d.ts",
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
