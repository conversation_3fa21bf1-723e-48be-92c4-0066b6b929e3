import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { EmailService } from './EmailService';

export class WebSocketService {
  private io: Server;
  private emailService: EmailService;
  private connectedClients: Map<string, Set<string>> = new Map(); // email -> socketIds

  constructor(io: Server) {
    this.io = io;
    this.emailService = new EmailService();
  }

  initialize() {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`WebSocket客户端连接: ${socket.id}`);

      // 处理订阅邮箱验证码
      socket.on('subscribe', (data: { email: string }) => {
        this.handleSubscribe(socket, data.email);
      });

      // 处理取消订阅
      socket.on('unsubscribe', (data: { email: string }) => {
        this.handleUnsubscribe(socket, data.email);
      });

      // 处理获取验证码
      socket.on('getVerificationCodes', async (data: { email: string }) => {
        await this.handleGetVerificationCodes(socket, data.email);
      });

      // 处理断开连接
      socket.on('disconnect', () => {
        this.handleDisconnect(socket);
      });

      // 发送连接成功消息
      socket.emit('connected', {
        message: '连接成功',
        timestamp: new Date().toISOString()
      });
    });

    // 监听邮件服务的验证码事件
    this.emailService.on('verificationCodeReceived', (data) => {
      this.broadcastVerificationCode(data);
    });

    logger.info('WebSocket服务已初始化');
  }

  private handleSubscribe(socket: Socket, email: string) {
    if (!email) {
      socket.emit('error', { message: '邮箱地址不能为空' });
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      socket.emit('error', { message: '邮箱格式不正确' });
      return;
    }

    // 添加到订阅列表
    if (!this.connectedClients.has(email)) {
      this.connectedClients.set(email, new Set());
    }
    this.connectedClients.get(email)!.add(socket.id);

    // 加入房间
    socket.join(`email:${email}`);

    logger.info(`客户端 ${socket.id} 订阅邮箱: ${email}`);

    // 发送订阅成功消息
    socket.emit('subscribed', {
      email,
      message: '订阅成功',
      timestamp: new Date().toISOString()
    });

    // 立即发送当前的验证码
    this.sendCurrentVerificationCodes(socket, email);
  }

  private handleUnsubscribe(socket: Socket, email: string) {
    if (!email) {
      socket.emit('error', { message: '邮箱地址不能为空' });
      return;
    }

    // 从订阅列表移除
    if (this.connectedClients.has(email)) {
      this.connectedClients.get(email)!.delete(socket.id);
      
      // 如果没有客户端订阅该邮箱，删除记录
      if (this.connectedClients.get(email)!.size === 0) {
        this.connectedClients.delete(email);
      }
    }

    // 离开房间
    socket.leave(`email:${email}`);

    logger.info(`客户端 ${socket.id} 取消订阅邮箱: ${email}`);

    // 发送取消订阅成功消息
    socket.emit('unsubscribed', {
      email,
      message: '取消订阅成功',
      timestamp: new Date().toISOString()
    });
  }

  private async handleGetVerificationCodes(socket: Socket, email: string) {
    try {
      if (!email) {
        socket.emit('error', { message: '邮箱地址不能为空' });
        return;
      }

      const codes = await this.emailService.getVerificationCodes(email);
      
      socket.emit('verificationCodes', {
        email,
        codes,
        timestamp: new Date().toISOString()
      });

      logger.info(`发送验证码列表给客户端 ${socket.id}: ${email} (${codes.length}条)`);

    } catch (error) {
      logger.error(`获取验证码失败 ${socket.id}:`, error);
      socket.emit('error', { 
        message: '获取验证码失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  private async sendCurrentVerificationCodes(socket: Socket, email: string) {
    try {
      const codes = await this.emailService.getVerificationCodes(email);
      
      socket.emit('verificationCodes', {
        email,
        codes,
        timestamp: new Date().toISOString()
      });

      logger.info(`发送当前验证码给新订阅客户端 ${socket.id}: ${email} (${codes.length}条)`);

    } catch (error) {
      logger.error(`发送当前验证码失败 ${socket.id}:`, error);
      socket.emit('error', { 
        message: '获取当前验证码失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  private handleDisconnect(socket: Socket) {
    logger.info(`WebSocket客户端断开连接: ${socket.id}`);

    // 从所有订阅中移除该客户端
    for (const [email, socketIds] of this.connectedClients.entries()) {
      socketIds.delete(socket.id);
      
      // 如果没有客户端订阅该邮箱，删除记录
      if (socketIds.size === 0) {
        this.connectedClients.delete(email);
      }
    }
  }

  private broadcastVerificationCode(data: any) {
    const { email, code, sender, timestamp, expiresAt } = data;
    
    // 计算剩余时间
    const now = new Date();
    const expires = new Date(expiresAt);
    const remainingTime = Math.max(0, Math.floor((expires.getTime() - now.getTime()) / 1000));

    const message = {
      email,
      code,
      sender,
      timestamp: timestamp.toISOString(),
      expiresAt: expiresAt.toISOString(),
      remainingTime,
      isExpired: remainingTime === 0
    };

    // 向订阅该邮箱的所有客户端广播
    this.io.to(`email:${email}`).emit('newVerificationCode', message);

    logger.info(`广播新验证码: ${email} - ${code} (${this.getSubscriberCount(email)}个订阅者)`);
  }

  private getSubscriberCount(email: string): number {
    return this.connectedClients.get(email)?.size || 0;
  }

  // 获取连接统计
  getStats() {
    const totalConnections = this.io.engine.clientsCount;
    const totalSubscriptions = Array.from(this.connectedClients.values())
      .reduce((sum, socketIds) => sum + socketIds.size, 0);
    
    return {
      totalConnections,
      totalSubscriptions,
      subscribedEmails: this.connectedClients.size,
      emailSubscriptions: Object.fromEntries(
        Array.from(this.connectedClients.entries()).map(([email, socketIds]) => [
          email,
          socketIds.size
        ])
      )
    };
  }

  // 手动广播消息（用于测试）
  broadcastMessage(email: string, message: any) {
    this.io.to(`email:${email}`).emit('message', {
      ...message,
      timestamp: new Date().toISOString()
    });
  }
}
