#!/bin/bash

# 邮箱验证码管理系统启动脚本

echo "🚀 启动邮箱验证码管理系统..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件，配置您的邮箱信息"
    echo "   特别是以下配置项："
    echo "   - IMAP_HOST: 您的IMAP服务器地址"
    echo "   - IMAP_USER: 您的邮箱地址"
    echo "   - IMAP_PASSWORD: 您的邮箱密码"
    read -p "配置完成后按回车继续..."
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p mysql/data
mkdir -p redis/data
mkdir -p backend/logs
mkdir -p nginx/logs

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ 系统启动完成！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost"
echo "   后端API: http://localhost:3000"
echo "   健康检查: http://localhost/health"
echo ""
echo "🔧 管理命令："
echo "   查看日志: docker-compose logs -f"
echo "   停止服务: docker-compose down"
echo "   重启服务: docker-compose restart"
echo ""
echo "📊 数据库连接信息："
echo "   MySQL: localhost:3306"
echo "   Redis: localhost:6379"
echo ""

# 检查服务健康状态
echo "🏥 检查服务健康状态..."
sleep 5

# 检查后端健康状态
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务可能未正常启动，请检查日志: docker-compose logs backend"
fi

# 检查前端健康状态
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务可能未正常启动，请检查日志: docker-compose logs nginx"
fi

echo ""
echo "🎉 系统已成功启动！请在浏览器中访问 http://localhost"
