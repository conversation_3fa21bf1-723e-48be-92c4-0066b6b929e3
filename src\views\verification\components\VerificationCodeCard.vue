<template>
  <el-card 
    class="code-card" 
    :class="{ 
      expired: code.isExpired,
      'near-expiry': isNearExpiry && !code.isExpired
    }"
    shadow="hover"
  >
    <!-- 卡片头部 -->
    <template #header>
      <div class="code-header">
        <div class="status-info">
          <el-tag 
            :type="code.isExpired ? 'info' : (isNearExpiry ? 'warning' : 'success')"
            size="small"
            effect="dark"
          >
            <el-icon>
              <CircleCheck v-if="!code.isExpired && !isNearExpiry" />
              <Warning v-else-if="!code.isExpired && isNearExpiry" />
              <CircleClose v-else />
            </el-icon>
            {{ getStatusText() }}
          </el-tag>
          
          <el-tooltip :content="code.sender" placement="top">
            <el-text type="info" size="small" truncated class="sender">
              来自：{{ code.sender }}
            </el-text>
          </el-tooltip>
        </div>
        
        <div class="actions">
          <el-button
            size="small"
            type="primary"
            :icon="DocumentCopy"
            @click="handleCopy"
            :disabled="code.isExpired"
            circle
          />
        </div>
      </div>
    </template>

    <!-- 验证码内容 -->
    <div class="code-content">
      <div class="verification-code" @click="handleCopy">
        <span class="code-text">{{ code.code }}</span>
        <el-icon class="copy-icon"><DocumentCopy /></el-icon>
      </div>
      
      <!-- 倒计时 -->
      <div v-if="!code.isExpired" class="countdown-section">
        <CountdownTimer 
          :expires-at="code.expiresAt"
          @expired="handleExpired"
          @near-expiry="handleNearExpiry"
        />
      </div>
      
      <!-- 已过期提示 -->
      <div v-else class="expired-section">
        <el-text type="info" size="small">
          <el-icon><Clock /></el-icon>
          已过期
        </el-text>
      </div>
    </div>

    <!-- 卡片底部 -->
    <template #footer>
      <div class="code-footer">
        <div class="time-info">
          <el-text type="info" size="small">
            <el-icon><Clock /></el-icon>
            {{ formatTime(code.timestamp) }}
          </el-text>
        </div>
        
        <div class="footer-actions">
          <el-button 
            size="small" 
            type="success"
            :icon="CircleCheck"
            @click="handleVerify"
            :disabled="code.isExpired"
            text
          >
            验证
          </el-button>
          
          <el-button 
            size="small" 
            type="primary"
            :icon="DocumentCopy"
            @click="handleCopy"
            :disabled="code.isExpired"
            text
          >
            复制
          </el-button>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  CircleCheck, 
  CircleClose, 
  Warning,
  DocumentCopy, 
  Clock 
} from '@element-plus/icons-vue'
import type { VerificationCode } from '@/stores/verification'
import CountdownTimer from './CountdownTimer.vue'

interface Props {
  code: VerificationCode
}

interface Emits {
  (e: 'copy', code: string): void
  (e: 'verify', email: string, code: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算属性
const isNearExpiry = computed(() => {
  return props.code.remainingTime > 0 && props.code.remainingTime <= 30
})

// 获取状态文本
const getStatusText = () => {
  if (props.code.isExpired) return '已过期'
  if (isNearExpiry.value) return '即将过期'
  return '有效'
}

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 处理复制
const handleCopy = () => {
  if (props.code.isExpired) {
    ElMessage.warning('验证码已过期，无法复制')
    return
  }
  
  emit('copy', props.code.code)
}

// 处理验证
const handleVerify = () => {
  if (props.code.isExpired) {
    ElMessage.warning('验证码已过期，无法验证')
    return
  }
  
  emit('verify', props.code.email, props.code.code)
}

// 处理过期
const handleExpired = () => {
  console.log('验证码已过期:', props.code.code)
}

// 处理即将过期
const handleNearExpiry = () => {
  console.log('验证码即将过期:', props.code.code)
}
</script>

<style scoped lang="scss">
.code-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  &.expired {
    opacity: 0.6;
    background-color: var(--el-fill-color-light);
    
    .verification-code {
      background-color: var(--el-fill-color);
      color: var(--el-text-color-disabled);
    }
  }

  &.near-expiry {
    border-color: var(--el-color-warning);
    
    .verification-code {
      background-color: var(--el-color-warning-light-9);
      border-color: var(--el-color-warning-light-5);
    }
  }

  .code-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;

    .status-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .sender {
        max-width: 150px;
      }
    }

    .actions {
      flex-shrink: 0;
    }
  }

  .code-content {
    padding: 16px 0;

    .verification-code {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 16px;
      background-color: var(--el-color-primary-light-9);
      border: 2px dashed var(--el-color-primary-light-5);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 12px;

      &:hover {
        background-color: var(--el-color-primary-light-8);
        border-color: var(--el-color-primary-light-3);
      }

      .code-text {
        font-size: 24px;
        font-weight: bold;
        font-family: 'Courier New', monospace;
        color: var(--el-color-primary);
        letter-spacing: 2px;
      }

      .copy-icon {
        color: var(--el-color-primary);
        opacity: 0.7;
        transition: opacity 0.2s;
      }

      &:hover .copy-icon {
        opacity: 1;
      }
    }

    .countdown-section {
      text-align: center;
    }

    .expired-section {
      text-align: center;
      padding: 8px;
    }
  }

  .code-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .time-info {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .footer-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .code-card {
    .code-header {
      flex-direction: column;
      align-items: stretch;

      .actions {
        align-self: flex-end;
      }
    }

    .code-content {
      .verification-code {
        .code-text {
          font-size: 20px;
        }
      }
    }

    .code-footer {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .footer-actions {
        justify-content: center;
      }
    }
  }
}
</style>
