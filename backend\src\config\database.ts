import mysql from 'mysql2/promise';
import { logger } from '../utils/logger';

let connection: mysql.Connection;

export async function connectDatabase() {
  try {
    connection = await mysql.createConnection({
      host: process.env.MYSQL_HOST || 'localhost',
      port: parseInt(process.env.MYSQL_PORT || '3306'),
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'email_verification',
      charset: 'utf8mb4',
      timezone: '+08:00'
    });

    logger.info('MySQL数据库连接成功');
    
    // 创建表
    await createTables();
    
  } catch (error) {
    logger.error('MySQL数据库连接失败:', error);
    throw error;
  }
}

async function createTables() {
  try {
    // 创建邮箱配置表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS email_configs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email_address VARCHAR(255) UNIQUE NOT NULL,
        domain VARCHAR(100) NOT NULL,
        imap_server VARCHAR(255) NOT NULL,
        imap_port INT DEFAULT 993,
        username VARCHAR(255) NOT NULL,
        password VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email_address),
        INDEX idx_domain (domain),
        INDEX idx_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 创建验证码日志表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS verification_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email_address VARCHAR(255) NOT NULL,
        verification_code VARCHAR(10) NOT NULL,
        sender VARCHAR(255),
        subject TEXT,
        received_at TIMESTAMP NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_email_received (email_address, received_at DESC),
        INDEX idx_expires (expires_at),
        INDEX idx_code (verification_code)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    logger.info('数据库表创建/检查完成');
  } catch (error) {
    logger.error('创建数据库表失败:', error);
    throw error;
  }
}

export function getDatabase() {
  if (!connection) {
    throw new Error('数据库未连接');
  }
  return connection;
}

export async function closeDatabase() {
  if (connection) {
    await connection.end();
    logger.info('数据库连接已关闭');
  }
}
