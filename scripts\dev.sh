#!/bin/bash

# 开发环境启动脚本

echo "🚀 启动开发环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查yarn是否安装
if ! command -v yarn &> /dev/null; then
    echo "❌ Yarn未安装，请先安装Yarn"
    exit 1
fi

# 启动数据库服务（仅MySQL和Redis）
echo "🗄️  启动数据库服务..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 安装前端依赖
echo "📦 安装前端依赖..."
yarn install

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
npm install
cd ..

# 创建后端环境变量文件
if [ ! -f backend/.env ]; then
    echo "📝 创建后端环境变量文件..."
    cp backend/.env.example backend/.env
fi

# 启动后端开发服务器
echo "🔧 启动后端开发服务器..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 5

# 启动前端开发服务器
echo "🎨 启动前端开发服务器..."
yarn dev &
FRONTEND_PID=$!

# 等待服务启动
sleep 3

echo ""
echo "✅ 开发环境启动完成！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:5173"
echo "   后端API: http://localhost:3000"
echo ""
echo "🔧 开发工具："
echo "   前端热重载: 已启用"
echo "   后端热重载: 已启用"
echo ""
echo "🛑 停止开发环境: Ctrl+C"

# 等待用户中断
trap "echo '🛑 停止开发环境...'; kill $BACKEND_PID $FRONTEND_PID; docker-compose stop mysql redis; exit" INT

# 保持脚本运行
wait
