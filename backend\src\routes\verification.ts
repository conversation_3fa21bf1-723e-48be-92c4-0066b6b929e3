import { Router } from 'express';
import { async<PERSON>and<PERSON>, AppError } from '../middleware/errorHandler';
import { apiRateLimit } from '../middleware/rateLimiter';
import { EmailService } from '../services/EmailService';
import { logger } from '../utils/logger';
import Joi from 'joi';

const router = Router();
const emailService = new EmailService();

// 验证邮箱格式的schema
const emailSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '邮箱格式不正确',
    'any.required': '邮箱地址不能为空'
  })
});

// 获取指定邮箱的验证码列表
router.get('/:email', apiRateLimit, asyncHandler(async (req, res) => {
  const { email } = req.params;

  // 验证邮箱格式
  const { error } = emailSchema.validate({ email });
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  try {
    const codes = await emailService.getVerificationCodes(email);
    
    logger.info(`获取验证码列表: ${email} (${codes.length}条)`);

    res.json({
      success: true,
      data: {
        email,
        codes,
        count: codes.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error(`获取验证码失败 ${email}:`, error);
    throw new AppError('获取验证码失败', 500);
  }
}));

// 获取指定邮箱的最新验证码
router.get('/:email/latest', apiRateLimit, asyncHandler(async (req, res) => {
  const { email } = req.params;

  // 验证邮箱格式
  const { error } = emailSchema.validate({ email });
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  try {
    const latestCode = await emailService.getLatestVerificationCode(email);
    
    if (!latestCode) {
      return res.json({
        success: true,
        data: null,
        message: '暂无验证码'
      });
    }

    logger.info(`获取最新验证码: ${email} - ${latestCode.code}`);

    res.json({
      success: true,
      data: {
        email,
        ...latestCode,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error(`获取最新验证码失败 ${email}:`, error);
    throw new AppError('获取最新验证码失败', 500);
  }
}));

// 验证验证码是否有效
router.post('/:email/verify', apiRateLimit, asyncHandler(async (req, res) => {
  const { email } = req.params;
  const { code } = req.body;

  // 验证请求参数
  const schema = Joi.object({
    email: Joi.string().email().required(),
    code: Joi.string().pattern(/^\d{6}$/).required().messages({
      'string.pattern.base': '验证码必须是6位数字'
    })
  });

  const { error } = schema.validate({ email, code });
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  try {
    const codes = await emailService.getVerificationCodes(email);
    const validCode = codes.find(c => c.code === code && !c.isExpired);

    if (validCode) {
      logger.info(`验证码验证成功: ${email} - ${code}`);
      
      res.json({
        success: true,
        data: {
          valid: true,
          code: validCode,
          message: '验证码有效'
        }
      });
    } else {
      logger.warn(`验证码验证失败: ${email} - ${code}`);
      
      res.json({
        success: true,
        data: {
          valid: false,
          message: '验证码无效或已过期'
        }
      });
    }

  } catch (error) {
    logger.error(`验证验证码失败 ${email}:`, error);
    throw new AppError('验证验证码失败', 500);
  }
}));

// 获取验证码统计信息
router.get('/:email/stats', apiRateLimit, asyncHandler(async (req, res) => {
  const { email } = req.params;

  // 验证邮箱格式
  const { error } = emailSchema.validate({ email });
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  try {
    const codes = await emailService.getVerificationCodes(email);
    
    const stats = {
      total: codes.length,
      valid: codes.filter(c => !c.isExpired).length,
      expired: codes.filter(c => c.isExpired).length,
      latest: codes.length > 0 ? codes[0] : null
    };

    logger.info(`获取验证码统计: ${email}`, stats);

    res.json({
      success: true,
      data: {
        email,
        stats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error(`获取验证码统计失败 ${email}:`, error);
    throw new AppError('获取验证码统计失败', 500);
  }
}));

export default router;
