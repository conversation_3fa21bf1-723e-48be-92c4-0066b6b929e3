import antfu from '@antfu/eslint-config'

// import { FlatCompat } from '@eslint/eslintrc'
// const compat = new FlatCompat()

// export default antfu({
//   formatters: true,
//   unocss: true,
//   vue: true,
// })

export default antfu(
  {
    // 项目类型: ‘lib’表示库，默认为‘app’
    type: 'lib',

    // 应用stylistic格式化规则
    stylistic: true,

    // 或者自定义stylistic规则
    // stylistic: {
    //   indent: 2, // 4, or 'tab'
    //   quotes: 'single', // or 'double'
    // },

    // TypeScript and Vue are autodetected, you can also explicitly enable them:
    typescript: true,
    vue: true,
    // 禁用对 JSONC/YAML 的支持
    jsonc: false,
    yaml: false,

    formatters: {
      /**
       * Format CSS, LESS, SCSS files, also the `<style>` blocks in Vue
       * By default uses Prettier
       */
      css: true,
      /**
       * Format HTML files
       * By default uses Prettier
       */
      html: true,
      /**
       * Format Markdown files
       * Supports Prettier and dprint
       * By default uses Prettier
       */
      markdown: 'prettier',
    },

    // languageOptions: {  // 解决eslint不了解vue3自动导入内容，会报undefine,但该版本anfu似乎解决了该问题
    //   globals: autoImport?.globals,
    // },

    // 禁用顶级函数风格
    lessOpinionated: true,

    // 忽略内容
    ignores: [
      '**/.*',
      'dist/*',
      '*.d.ts',
      'public/*',
      'src/assets/**',
      // ...globs
    ],
  },
  // ...compat.config({ // 解决eslint不了解vue3自动导入内容，会报undefine,但该版本anfu似乎解决了该问题
  //   extends: [
  //     './.eslintrc-auto-import.json',
  //   ],
  // }),
  {
    rules: {
      'no-console': 'warn', // 可以改为off
    },
  },
  // 自定义配置
  // {
  //   files: ['**/*.ts'],
  //   rules: {
  //     'vue/operator-linebreak': ['error', 'before'],
  //     'style/semi': ['error', 'never'],
  //   },
  // },
)
