{"name": "email-verification-backend", "version": "1.0.0", "description": "邮箱验证码管理系统后端", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "node-imap": "^0.9.6", "ioredis": "^5.3.2", "mysql2": "^3.6.0", "socket.io": "^4.7.2", "bcrypt": "^5.1.0", "joi": "^17.9.2", "winston": "^3.10.0", "nodemailer": "^6.9.4", "mailparser": "^3.6.5", "dotenv": "^16.3.1", "uuid": "^9.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/node": "^20.5.0", "@types/bcrypt": "^5.0.0", "@types/uuid": "^9.0.2", "@types/node-imap": "^0.9.2", "typescript": "^5.1.6", "tsx": "^3.12.7", "jest": "^29.6.2", "@types/jest": "^29.5.3"}, "engines": {"node": ">=18.0.0"}}