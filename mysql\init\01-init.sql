-- 初始化数据库脚本

-- 创建邮箱配置表
CREATE TABLE IF NOT EXISTS email_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email_address VARCHAR(255) UNIQUE NOT NULL,
    domain VARCHAR(100) NOT NULL,
    imap_server VARCHAR(255) NOT NULL,
    imap_port INT DEFAULT 993,
    username VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email_address),
    INDEX idx_domain (domain),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建验证码日志表
CREATE TABLE IF NOT EXISTS verification_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email_address VARCHAR(255) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    sender VARCHAR(255),
    subject TEXT,
    received_at TIMESTAMP NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email_received (email_address, received_at DESC),
    INDEX idx_expires (expires_at),
    INDEX idx_code (verification_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例邮箱配置（可选，用于测试）
-- INSERT INTO email_configs (email_address, domain, imap_server, imap_port, username, password, is_active) 
-- VALUES ('<EMAIL>', 'xxyxhui.online', 'imap.xxyxhui.online', 993, '<EMAIL>', 'encrypted_password', true);

-- 创建用户和权限
CREATE USER IF NOT EXISTS 'appuser'@'%' IDENTIFIED BY 'apppassword123';
GRANT SELECT, INSERT, UPDATE, DELETE ON email_verification.* TO 'appuser'@'%';
FLUSH PRIVILEGES;
