import { Router } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON>, AppError } from '../middleware/errorHandler';
import { strictRateLimit } from '../middleware/rateLimiter';
import { getDatabase } from '../config/database';
import { logger } from '../utils/logger';
import bcrypt from 'bcrypt';
import Joi from 'joi';

const router = Router();

// 邮箱配置验证schema
const emailConfigSchema = Joi.object({
  email_address: Joi.string().email().required().messages({
    'string.email': '邮箱格式不正确',
    'any.required': '邮箱地址不能为空'
  }),
  domain: Joi.string().required().messages({
    'any.required': '域名不能为空'
  }),
  imap_server: Joi.string().required().messages({
    'any.required': 'IMAP服务器不能为空'
  }),
  imap_port: Joi.number().integer().min(1).max(65535).default(993),
  username: Joi.string().required().messages({
    'any.required': '用户名不能为空'
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': '密码至少6位',
    'any.required': '密码不能为空'
  }),
  is_active: Joi.boolean().default(true)
});

const updateEmailConfigSchema = Joi.object({
  email_address: Joi.string().email(),
  domain: Joi.string(),
  imap_server: Joi.string(),
  imap_port: Joi.number().integer().min(1).max(65535),
  username: Joi.string(),
  password: Joi.string().min(6),
  is_active: Joi.boolean()
}).min(1);

// 获取所有邮箱配置
router.get('/', asyncHandler(async (req, res) => {
  try {
    const db = getDatabase();
    const [rows] = await db.execute(`
      SELECT id, email_address, domain, imap_server, imap_port, 
             username, is_active, created_at, updated_at 
      FROM email_configs 
      ORDER BY created_at DESC
    `);

    logger.info(`获取邮箱配置列表: ${(rows as any[]).length}条`);

    res.json({
      success: true,
      data: rows,
      count: (rows as any[]).length
    });

  } catch (error) {
    logger.error('获取邮箱配置失败:', error);
    throw new AppError('获取邮箱配置失败', 500);
  }
}));

// 获取单个邮箱配置
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(Number(id))) {
    throw new AppError('无效的配置ID', 400);
  }

  try {
    const db = getDatabase();
    const [rows] = await db.execute(`
      SELECT id, email_address, domain, imap_server, imap_port, 
             username, is_active, created_at, updated_at 
      FROM email_configs 
      WHERE id = ?
    `, [id]);

    const configs = rows as any[];
    if (configs.length === 0) {
      throw new AppError('邮箱配置不存在', 404);
    }

    logger.info(`获取邮箱配置: ${id}`);

    res.json({
      success: true,
      data: configs[0]
    });

  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error(`获取邮箱配置失败 ${id}:`, error);
    throw new AppError('获取邮箱配置失败', 500);
  }
}));

// 创建邮箱配置
router.post('/', strictRateLimit, asyncHandler(async (req, res) => {
  const { error, value } = emailConfigSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const {
    email_address,
    domain,
    imap_server,
    imap_port,
    username,
    password,
    is_active
  } = value;

  try {
    const db = getDatabase();

    // 检查邮箱是否已存在
    const [existing] = await db.execute(
      'SELECT id FROM email_configs WHERE email_address = ?',
      [email_address]
    );

    if ((existing as any[]).length > 0) {
      throw new AppError('邮箱配置已存在', 409);
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 插入新配置
    const [result] = await db.execute(`
      INSERT INTO email_configs 
      (email_address, domain, imap_server, imap_port, username, password, is_active) 
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [email_address, domain, imap_server, imap_port, username, hashedPassword, is_active]);

    const insertId = (result as any).insertId;

    logger.info(`创建邮箱配置成功: ${email_address} (ID: ${insertId})`);

    res.status(201).json({
      success: true,
      data: {
        id: insertId,
        email_address,
        domain,
        imap_server,
        imap_port,
        username,
        is_active,
        message: '邮箱配置创建成功'
      }
    });

  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error('创建邮箱配置失败:', error);
    throw new AppError('创建邮箱配置失败', 500);
  }
}));

// 更新邮箱配置
router.put('/:id', strictRateLimit, asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(Number(id))) {
    throw new AppError('无效的配置ID', 400);
  }

  const { error, value } = updateEmailConfigSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  try {
    const db = getDatabase();

    // 检查配置是否存在
    const [existing] = await db.execute(
      'SELECT id FROM email_configs WHERE id = ?',
      [id]
    );

    if ((existing as any[]).length === 0) {
      throw new AppError('邮箱配置不存在', 404);
    }

    // 构建更新语句
    const updateFields = [];
    const updateValues = [];

    for (const [key, val] of Object.entries(value)) {
      if (key === 'password') {
        // 加密新密码
        const hashedPassword = await bcrypt.hash(val as string, 12);
        updateFields.push(`${key} = ?`);
        updateValues.push(hashedPassword);
      } else {
        updateFields.push(`${key} = ?`);
        updateValues.push(val);
      }
    }

    updateValues.push(id);

    await db.execute(`
      UPDATE email_configs 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `, updateValues);

    logger.info(`更新邮箱配置成功: ID ${id}`);

    res.json({
      success: true,
      data: {
        id: Number(id),
        ...value,
        message: '邮箱配置更新成功'
      }
    });

  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error(`更新邮箱配置失败 ${id}:`, error);
    throw new AppError('更新邮箱配置失败', 500);
  }
}));

// 删除邮箱配置
router.delete('/:id', strictRateLimit, asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(Number(id))) {
    throw new AppError('无效的配置ID', 400);
  }

  try {
    const db = getDatabase();

    // 检查配置是否存在
    const [existing] = await db.execute(
      'SELECT email_address FROM email_configs WHERE id = ?',
      [id]
    );

    if ((existing as any[]).length === 0) {
      throw new AppError('邮箱配置不存在', 404);
    }

    const emailAddress = (existing as any[])[0].email_address;

    // 删除配置
    await db.execute('DELETE FROM email_configs WHERE id = ?', [id]);

    logger.info(`删除邮箱配置成功: ${emailAddress} (ID: ${id})`);

    res.json({
      success: true,
      data: {
        id: Number(id),
        email_address: emailAddress,
        message: '邮箱配置删除成功'
      }
    });

  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error(`删除邮箱配置失败 ${id}:`, error);
    throw new AppError('删除邮箱配置失败', 500);
  }
}));

// 测试邮箱连接
router.post('/:id/test', strictRateLimit, asyncHandler(async (req, res) => {
  const { id } = req.params;

  if (!id || isNaN(Number(id))) {
    throw new AppError('无效的配置ID', 400);
  }

  try {
    const db = getDatabase();
    const [rows] = await db.execute(
      'SELECT * FROM email_configs WHERE id = ?',
      [id]
    );

    const configs = rows as any[];
    if (configs.length === 0) {
      throw new AppError('邮箱配置不存在', 404);
    }

    const config = configs[0];

    // TODO: 实现IMAP连接测试
    // 这里可以创建一个临时的IMAP连接来测试配置是否正确

    logger.info(`测试邮箱连接: ${config.email_address}`);

    res.json({
      success: true,
      data: {
        id: Number(id),
        email_address: config.email_address,
        status: 'success',
        message: '连接测试成功'
      }
    });

  } catch (error) {
    if (error instanceof AppError) throw error;
    logger.error(`测试邮箱连接失败 ${id}:`, error);
    throw new AppError('测试邮箱连接失败', 500);
  }
}));

export default router;
