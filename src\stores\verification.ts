import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { io, Socket } from 'socket.io-client'
import { ElMessage } from 'element-plus'

export interface VerificationCode {
  code: string
  timestamp: string
  expiresAt: string
  remainingTime: number
  sender: string
  isExpired: boolean
  email: string
}

export const useVerificationStore = defineStore('verification', () => {
  // 状态
  const currentEmail = ref('')
  const verificationCodes = ref<VerificationCode[]>([])
  const isLoading = ref(false)
  const error = ref('')
  const socket = ref<Socket | null>(null)
  const isConnected = ref(false)

  // API基础URL
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'
  const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:3000'

  // 计算属性
  const validCodes = computed(() => 
    verificationCodes.value.filter(code => !code.isExpired)
  )

  const expiredCodes = computed(() => 
    verificationCodes.value.filter(code => code.isExpired)
  )

  const latestCode = computed(() => 
    verificationCodes.value.length > 0 ? verificationCodes.value[0] : null
  )

  // 初始化WebSocket连接
  const initWebSocket = () => {
    if (socket.value?.connected) return

    socket.value = io(WS_URL, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    })

    socket.value.on('connect', () => {
      isConnected.value = true
      console.log('WebSocket连接成功')
    })

    socket.value.on('disconnect', () => {
      isConnected.value = false
      console.log('WebSocket连接断开')
    })

    socket.value.on('connected', (data) => {
      console.log('服务器确认连接:', data)
    })

    socket.value.on('subscribed', (data) => {
      console.log('订阅成功:', data)
      ElMessage.success(`已订阅邮箱 ${data.email} 的验证码更新`)
    })

    socket.value.on('unsubscribed', (data) => {
      console.log('取消订阅成功:', data)
    })

    socket.value.on('verificationCodes', (data) => {
      console.log('收到验证码列表:', data)
      verificationCodes.value = data.codes || []
      updateRemainingTimes()
    })

    socket.value.on('newVerificationCode', (data) => {
      console.log('收到新验证码:', data)
      
      // 添加新验证码到列表顶部
      const newCode: VerificationCode = {
        code: data.code,
        timestamp: data.timestamp,
        expiresAt: data.expiresAt,
        remainingTime: data.remainingTime,
        sender: data.sender,
        isExpired: data.isExpired,
        email: data.email
      }

      // 检查是否已存在相同的验证码
      const existingIndex = verificationCodes.value.findIndex(
        code => code.code === newCode.code && code.timestamp === newCode.timestamp
      )

      if (existingIndex === -1) {
        verificationCodes.value.unshift(newCode)
        ElMessage.success(`收到新验证码: ${newCode.code}`)
      }
    })

    socket.value.on('error', (data) => {
      console.error('WebSocket错误:', data)
      error.value = data.message || '连接错误'
      ElMessage.error(data.message || '连接错误')
    })

    socket.value.on('connect_error', (err) => {
      console.error('WebSocket连接错误:', err)
      error.value = '连接失败'
    })
  }

  // 订阅邮箱验证码更新
  const subscribeToEmail = (email: string) => {
    if (!socket.value?.connected) {
      initWebSocket()
      
      // 等待连接成功后再订阅
      socket.value?.on('connect', () => {
        socket.value?.emit('subscribe', { email })
      })
    } else {
      socket.value.emit('subscribe', { email })
    }
  }

  // 取消订阅
  const unsubscribeFromEmail = (email: string) => {
    if (socket.value?.connected) {
      socket.value.emit('unsubscribe', { email })
    }
  }

  // 获取验证码列表
  const fetchVerificationCodes = async (email: string) => {
    if (!email) {
      error.value = '邮箱地址不能为空'
      return
    }

    isLoading.value = true
    error.value = ''

    try {
      const response = await axios.get(`${API_BASE_URL}/api/verification-codes/${encodeURIComponent(email)}`)
      
      if (response.data.success) {
        verificationCodes.value = response.data.data.codes || []
        currentEmail.value = email
        updateRemainingTimes()
        
        // 订阅实时更新
        subscribeToEmail(email)
      } else {
        throw new Error(response.data.message || '获取验证码失败')
      }
    } catch (err: any) {
      const message = err.response?.data?.error?.message || err.message || '获取验证码失败'
      error.value = message
      ElMessage.error(message)
      verificationCodes.value = []
    } finally {
      isLoading.value = false
    }
  }

  // 获取最新验证码
  const fetchLatestCode = async (email: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/verification-codes/${encodeURIComponent(email)}/latest`)
      
      if (response.data.success && response.data.data) {
        return response.data.data
      }
      return null
    } catch (err: any) {
      console.error('获取最新验证码失败:', err)
      return null
    }
  }

  // 验证验证码
  const verifyCode = async (email: string, code: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/verification-codes/${encodeURIComponent(email)}/verify`, {
        code
      })
      
      if (response.data.success) {
        return response.data.data
      }
      return { valid: false, message: '验证失败' }
    } catch (err: any) {
      const message = err.response?.data?.error?.message || err.message || '验证失败'
      ElMessage.error(message)
      return { valid: false, message }
    }
  }

  // 更新剩余时间
  const updateRemainingTimes = () => {
    const now = new Date()
    
    verificationCodes.value = verificationCodes.value.map(code => {
      const expiresAt = new Date(code.expiresAt)
      const remainingTime = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000))
      
      return {
        ...code,
        remainingTime,
        isExpired: remainingTime === 0
      }
    })
  }

  // 启动定时器更新剩余时间
  const startTimer = () => {
    setInterval(() => {
      if (verificationCodes.value.length > 0) {
        updateRemainingTimes()
      }
    }, 1000)
  }

  // 清空数据
  const clearData = () => {
    if (currentEmail.value) {
      unsubscribeFromEmail(currentEmail.value)
    }
    
    currentEmail.value = ''
    verificationCodes.value = []
    error.value = ''
  }

  // 断开WebSocket连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
      isConnected.value = false
    }
  }

  // 复制验证码到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      ElMessage.success('验证码已复制到剪贴板')
      return true
    } catch (err) {
      console.error('复制失败:', err)
      ElMessage.error('复制失败')
      return false
    }
  }

  return {
    // 状态
    currentEmail,
    verificationCodes,
    isLoading,
    error,
    isConnected,
    
    // 计算属性
    validCodes,
    expiredCodes,
    latestCode,
    
    // 方法
    initWebSocket,
    fetchVerificationCodes,
    fetchLatestCode,
    verifyCode,
    subscribeToEmail,
    unsubscribeFromEmail,
    updateRemainingTimes,
    startTimer,
    clearData,
    disconnect,
    copyToClipboard
  }
})
