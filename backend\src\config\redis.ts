import Redis from 'ioredis';
import { logger } from '../utils/logger';

let redis: Redis;

export async function connectRedis() {
  try {
    redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      retryDelayOnFailover: 100,
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
      lazyConnect: true
    });

    await redis.connect();
    
    redis.on('connect', () => {
      logger.info('Redis连接成功');
    });

    redis.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });

    redis.on('close', () => {
      logger.warn('Redis连接已关闭');
    });

    // 测试连接
    await redis.ping();
    logger.info('Redis连接测试成功');

  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

export function getRedis() {
  if (!redis) {
    throw new Error('Redis未连接');
  }
  return redis;
}

export async function closeRedis() {
  if (redis) {
    await redis.quit();
    logger.info('Redis连接已关闭');
  }
}

// Redis键名常量
export const REDIS_KEYS = {
  VERIFICATION_CODE: (email: string, timestamp: number) => `verification:${email}:${timestamp}`,
  LATEST_CODE: (email: string) => `email:latest:${email}`,
  EMAIL_CODES: (email: string) => `email:codes:${email}`,
  RATE_LIMIT: (ip: string) => `rate_limit:${ip}`
};

// 验证码相关操作
export class VerificationCodeCache {
  static async saveCode(email: string, code: string, sender: string, expiresAt: Date) {
    const timestamp = Date.now();
    const key = REDIS_KEYS.VERIFICATION_CODE(email, timestamp);
    const latestKey = REDIS_KEYS.LATEST_CODE(email);
    
    const data = {
      code,
      sender,
      timestamp,
      expiresAt: expiresAt.toISOString(),
      email
    };

    const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000);
    
    if (ttl > 0) {
      await redis.setex(key, ttl, JSON.stringify(data));
      await redis.set(latestKey, timestamp.toString());
      
      // 添加到邮箱验证码列表
      const listKey = REDIS_KEYS.EMAIL_CODES(email);
      await redis.zadd(listKey, timestamp, key);
      await redis.expire(listKey, 300); // 5分钟过期
      
      logger.info(`验证码已保存: ${email} - ${code}`);
    }
  }

  static async getCodes(email: string): Promise<any[]> {
    const listKey = REDIS_KEYS.EMAIL_CODES(email);
    const keys = await redis.zrevrange(listKey, 0, -1);
    
    const codes = [];
    for (const key of keys) {
      const data = await redis.get(key);
      if (data) {
        const codeData = JSON.parse(data);
        const now = new Date();
        const expiresAt = new Date(codeData.expiresAt);
        
        codes.push({
          ...codeData,
          isExpired: now > expiresAt,
          remainingTime: Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000))
        });
      }
    }
    
    return codes;
  }

  static async getLatestCode(email: string): Promise<any | null> {
    const latestKey = REDIS_KEYS.LATEST_CODE(email);
    const timestamp = await redis.get(latestKey);
    
    if (timestamp) {
      const key = REDIS_KEYS.VERIFICATION_CODE(email, parseInt(timestamp));
      const data = await redis.get(key);
      
      if (data) {
        const codeData = JSON.parse(data);
        const now = new Date();
        const expiresAt = new Date(codeData.expiresAt);
        
        return {
          ...codeData,
          isExpired: now > expiresAt,
          remainingTime: Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000))
        };
      }
    }
    
    return null;
  }
}
