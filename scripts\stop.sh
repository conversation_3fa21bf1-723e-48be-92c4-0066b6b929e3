#!/bin/bash

# 邮箱验证码管理系统停止脚本

echo "🛑 停止邮箱验证码管理系统..."

# 检查Docker Compose是否存在
if [ ! -f docker-compose.yml ]; then
    echo "❌ 未找到 docker-compose.yml 文件"
    exit 1
fi

# 停止所有服务
echo "🔄 停止所有服务..."
docker-compose down

# 可选：清理数据卷（谨慎使用）
read -p "是否要清理所有数据？这将删除数据库和Redis中的所有数据 (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理数据卷..."
    docker-compose down -v
    echo "✅ 数据已清理"
else
    echo "📦 数据已保留"
fi

# 可选：清理镜像
read -p "是否要清理构建的镜像？ (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🗑️  清理镜像..."
    docker-compose down --rmi all
    echo "✅ 镜像已清理"
fi

echo ""
echo "✅ 系统已停止"
echo ""
echo "🔧 如需重新启动，请运行: ./scripts/start.sh"
