<template>
  <div class="countdown-timer" :class="{ 'near-expiry': isNearExpiry, 'expired': isExpired }">
    <div class="timer-display">
      <el-icon class="timer-icon">
        <Timer v-if="!isExpired" />
        <CircleClose v-else />
      </el-icon>
      
      <span class="time-text">
        {{ isExpired ? '已过期' : formatTime(remainingTime) }}
      </span>
    </div>
    
    <!-- 进度条 -->
    <el-progress
      v-if="!isExpired"
      :percentage="progressPercentage"
      :color="progressColor"
      :stroke-width="4"
      :show-text="false"
      class="progress-bar"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Timer, CircleClose } from '@element-plus/icons-vue'

interface Props {
  expiresAt: string
}

interface Emits {
  (e: 'expired'): void
  (e: 'near-expiry'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 状态
const remainingTime = ref(0)
const totalTime = ref(120) // 2分钟 = 120秒
const timer = ref<NodeJS.Timeout | null>(null)
const hasEmittedNearExpiry = ref(false)
const hasEmittedExpired = ref(false)

// 计算属性
const isExpired = computed(() => remainingTime.value <= 0)

const isNearExpiry = computed(() => 
  remainingTime.value > 0 && remainingTime.value <= 30
)

const progressPercentage = computed(() => {
  if (totalTime.value === 0) return 0
  return Math.max(0, (remainingTime.value / totalTime.value) * 100)
})

const progressColor = computed(() => {
  if (isNearExpiry.value) return '#E6A23C' // 警告色
  return '#67C23A' // 成功色
})

// 格式化时间显示
const formatTime = (seconds: number) => {
  if (seconds <= 0) return '00:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 计算剩余时间
const calculateRemainingTime = () => {
  const now = new Date().getTime()
  const expiryTime = new Date(props.expiresAt).getTime()
  const remaining = Math.max(0, Math.floor((expiryTime - now) / 1000))
  
  return remaining
}

// 更新倒计时
const updateCountdown = () => {
  const newRemainingTime = calculateRemainingTime()
  remainingTime.value = newRemainingTime

  // 发出即将过期事件（只发出一次）
  if (isNearExpiry.value && !hasEmittedNearExpiry.value) {
    hasEmittedNearExpiry.value = true
    emit('near-expiry')
  }

  // 发出过期事件（只发出一次）
  if (isExpired.value && !hasEmittedExpired.value) {
    hasEmittedExpired.value = true
    emit('expired')
    stopTimer()
  }
}

// 启动定时器
const startTimer = () => {
  if (timer.value) return

  // 初始计算
  const initialRemaining = calculateRemainingTime()
  remainingTime.value = initialRemaining
  
  // 如果已经过期，直接返回
  if (initialRemaining <= 0) {
    emit('expired')
    return
  }

  // 设置总时间（用于进度条计算）
  if (totalTime.value === 120) {
    totalTime.value = initialRemaining
  }

  timer.value = setInterval(updateCountdown, 1000)
}

// 停止定时器
const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 重置定时器
const resetTimer = () => {
  stopTimer()
  hasEmittedNearExpiry.value = false
  hasEmittedExpired.value = false
  startTimer()
}

// 暴露方法
defineExpose({
  resetTimer,
  stopTimer
})

// 生命周期
onMounted(() => {
  startTimer()
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped lang="scss">
.countdown-timer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
  transition: all 0.3s ease;

  &.near-expiry {
    background-color: var(--el-color-warning-light-9);
    border: 1px solid var(--el-color-warning-light-5);

    .timer-display {
      color: var(--el-color-warning);
    }
  }

  &.expired {
    background-color: var(--el-color-danger-light-9);
    border: 1px solid var(--el-color-danger-light-5);

    .timer-display {
      color: var(--el-color-danger);
    }
  }

  .timer-display {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    transition: color 0.3s ease;

    .timer-icon {
      font-size: 16px;
    }

    .time-text {
      font-family: 'Courier New', monospace;
      font-size: 14px;
      min-width: 40px;
      text-align: center;
    }
  }

  .progress-bar {
    width: 100%;
    
    :deep(.el-progress-bar__outer) {
      border-radius: 2px;
    }
    
    :deep(.el-progress-bar__inner) {
      border-radius: 2px;
      transition: all 0.3s ease;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.countdown-timer.near-expiry {
  animation: pulse 2s infinite;
}

// 响应式设计
@media (max-width: 768px) {
  .countdown-timer {
    padding: 8px;

    .timer-display {
      .time-text {
        font-size: 12px;
      }
    }
  }
}
</style>
